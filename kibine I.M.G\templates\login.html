<!DOCTYPE html>
<html lang="{{ get_locale() }}" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>{{ _('تسجيل الدخول') }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            background: linear-gradient(120deg, #2980b9, #6dd5fa);
            font-family: 'Cairo', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .login-container {
            background: #fff;
            padding: 2.5rem 2rem;
            border-radius: 18px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
            width: 340px;
            text-align: center;
        }
        .login-container h2 {
            margin-bottom: 1.5rem;
            color: #2980b9;
        }
        .login-container input {
            width: 90%;
            padding: 0.7rem;
            margin: 0.7rem 0;
            border: 1px solid #b2bec3;
            border-radius: 8px;
            font-size: 1rem;
            outline: none;
        }
        .login-container button {
            width: 100%;
            padding: 0.8rem;
            background: #27ae60;
            color: #fff;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: background 0.2s;
        }
        .login-container button:hover {
            background: #219150;
        }
        .flash {
            color: #e74c3c;
            margin-bottom: 1rem;
        }
        .lang-switch {
            margin-bottom: 1.2rem;
            text-align: left;
        }
        .lang-switch a {
            color: #2980b9;
            text-decoration: none;
            margin: 0 0.3rem;
            font-weight: bold;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <div>
        <div class="lang-switch">
            <a href="?lang=ar">العربية</a> |
            <a href="?lang=en">English</a> |
            <a href="?lang=fr">Français</a>
        </div>
        <form class="login-container" method="POST">
            <h2>{{ _('تسجيل الدخول') }}</h2>
            {% with messages = get_flashed_messages() %}
              {% if messages %}
                <div class="flash">{{ messages[0] }}</div>
              {% endif %}
            {% endwith %}
            <input type="email" name="email" placeholder="{{ _('البريد الإلكتروني') }}" required>
            <input type="password" name="password" placeholder="{{ _('كلمة السر') }}" required>
            <button type="submit">{{ _('دخول') }}</button>
        </form>
    </div>
</body>
</html> 