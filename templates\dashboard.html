<!DOCTYPE html>
<html lang="{{ get_locale() }}" dir="{% if get_locale() == 'ar' %}rtl{% else %}ltr{% endif %}">
<head>
    <meta charset="UTF-8">
    <title>{{ _('لوحة تحكم المسؤولين') }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            background: linear-gradient(120deg, #6dd5fa, #2980b9);
            font-family: {% if get_locale() == 'ar' %}'Cairo', sans-serif{% else %}'Roboto', sans-serif{% endif %};
            margin: 0;
            min-height: 100vh;
        }
        .dashboard-container {
            max-width: 500px;
            margin: 40px auto;
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
            padding: 2.5rem 2rem 2rem 2rem;
        }
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        .dashboard-header h2 {
            color: #2980b9;
            margin: 0;
        }
        .logout-btn {
            background: #e74c3c;
            color: #fff;
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1.2rem;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.2s;
        }
        .logout-btn:hover {
            background: #c0392b;
        }
        .admins-list {
            margin-bottom: 2rem;
        }
        .admin-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.7rem 0.2rem;
            border-bottom: 1px solid #ecf0f1;
        }
        .admin-info {
            display: flex;
            align-items: center;
            gap: 0.7rem;
        }
        .status-dot {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 0.3rem;
        }
        .online {
            background: #27ae60;
            box-shadow: 0 0 8px #27ae60aa;
        }
        .offline {
            background: #e74c3c;
            box-shadow: 0 0 8px #e74c3c88;
        }
        .delete-btn {
            background: #e74c3c;
            color: #fff;
            border: none;
            border-radius: 6px;
            padding: 0.3rem 0.9rem;
            font-size: 0.95rem;
            cursor: pointer;
            transition: background 0.2s;
        }
        .delete-btn:hover {
            background: #c0392b;
        }
        .add-admin-form {
            display: flex;
            gap: 0.5rem;
            margin-top: 1.5rem;
            flex-wrap: wrap;
        }
        .add-admin-form input {
            flex: 1 1 120px;
            padding: 0.5rem;
            border: 1px solid #b2bec3;
            border-radius: 6px;
            font-size: 1rem;
        }
        .add-admin-form button {
            background: #2980b9;
            color: #fff;
            border: none;
            border-radius: 6px;
            padding: 0.5rem 1.2rem;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.2s;
        }
        .add-admin-form button:hover {
            background: #2471a3;
        }
        .flash {
            color: #27ae60;
            margin-bottom: 1rem;
            text-align: center;
        }
        .lang-switch {
            margin-bottom: 1.2rem;
            text-align: {% if get_locale() == 'ar' %}right{% else %}left{% endif %};
        }
        .lang-switch a {
            color: #2980b9;
            text-decoration: none;
            margin: 0 0.3rem;
            font-weight: bold;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&family=Roboto:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <div class="lang-switch">
            <a href="?lang=ar">العربية</a> |
            <a href="?lang=en">English</a> |
            <a href="?lang=fr">Français</a>
        </div>
        <div class="dashboard-header">
            <h2>{{ _('مرحبًا') }} {{ current_admin }}</h2>
            <form action="{{ url_for('logout', lang=get_locale()) }}" method="get" style="margin:0;">
                <button class="logout-btn" type="submit">{{ _('تسجيل الخروج') }}</button>
            </form>
        </div>
        {% with messages = get_flashed_messages() %}
          {% if messages %}
            <div class="flash">{{ messages[0] }}</div>
          {% endif %}
        {% endwith %}
        <div class="admins-list">
            {% for admin in admins %}
            <div class="admin-row">
                <div class="admin-info">
                    <span class="status-dot {% if admin.online %}online{% else %}offline{% endif %}"></span>
                    <span>{{ admin.name }}</span>
                </div>
                <form action="{{ url_for('delete_admin', email=admin.email, lang=get_locale()) }}" method="post" style="margin:0;">
                    <button class="delete-btn" type="submit">{{ _('حذف') }}</button>
                </form>
            </div>
            {% endfor %}
        </div>
        <form class="add-admin-form" action="{{ url_for('add_admin', lang=get_locale()) }}" method="post">
            <input type="text" name="name" placeholder="{{ _('اسم المسؤول') }}" required>
            <input type="email" name="email" placeholder="{{ _('البريد الإلكتروني') }}" required>
            <input type="password" name="password" placeholder="{{ _('كلمة السر') }}" required>
            <button type="submit">{{ _('إضافة مسؤول') }}</button>
        </form>
    </div>
</body>
</html> 