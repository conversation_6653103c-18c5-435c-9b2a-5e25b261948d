from flask import Flask, render_template, request, redirect, url_for, flash, session
from flask_babel import Babel, _, get_locale
import os
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

# إعداد Babel للترجمة
babel = Babel(app)

# قاعدة بيانات بسيطة في الذاكرة للمسؤولين
admins_db = [
    {'name': 'أحمد محمد', 'email': '<EMAIL>', 'password': '123456', 'online': True},
    {'name': 'فاطمة علي', 'email': '<EMAIL>', 'password': '123456', 'online': False}
]

# اللغات المدعومة
LANGUAGES = {
    'ar': 'العربية',
    'en': 'English',
    'fr': 'Français'
}

@babel.localeselector
def get_locale():
    # التحقق من اللغة في URL
    if request.args.get('lang'):
        session['language'] = request.args.get('lang')
    return session.get('language', 'ar')

@app.route('/')
@app.route('/<lang>')
def login(lang='ar'):
    session['language'] = lang
    return render_template('login.html')

@app.route('/', methods=['POST'])
@app.route('/<lang>', methods=['POST'])
def login_post(lang='ar'):
    session['language'] = lang
    email = request.form['email']
    password = request.form['password']

    # التحقق من بيانات المسؤول
    for admin in admins_db:
        if admin['email'] == email and admin['password'] == password:
            session['current_admin'] = admin['name']
            return redirect(url_for('dashboard', lang=lang))

    flash('بيانات الدخول غير صحيحة')
    return render_template('login.html')

@app.route('/dashboard')
@app.route('/dashboard/<lang>')
def dashboard(lang='ar'):
    session['language'] = lang
    if 'current_admin' not in session:
        return redirect(url_for('login', lang=lang))

    return render_template('dashboard.html',
                         admins=admins_db,
                         current_admin=session['current_admin'])

@app.route('/add_admin/<lang>', methods=['POST'])
def add_admin(lang='ar'):
    if 'current_admin' not in session:
        return redirect(url_for('login', lang=lang))

    name = request.form['name']
    email = request.form['email']
    password = request.form['password']

    # التحقق من عدم وجود البريد الإلكتروني مسبقاً
    for admin in admins_db:
        if admin['email'] == email:
            flash('البريد الإلكتروني موجود مسبقاً')
            return redirect(url_for('dashboard', lang=lang))

    # إضافة المسؤول الجديد
    admins_db.append({
        'name': name,
        'email': email,
        'password': password,
        'online': False
    })

    flash('تم إضافة المسؤول بنجاح')
    return redirect(url_for('dashboard', lang=lang))

@app.route('/delete_admin/<email>/<lang>', methods=['POST'])
def delete_admin(email, lang='ar'):
    if 'current_admin' not in session:
        return redirect(url_for('login', lang=lang))

    global admins_db
    admins_db = [admin for admin in admins_db if admin['email'] != email]

    flash('تم حذف المسؤول بنجاح')
    return redirect(url_for('dashboard', lang=lang))

@app.route('/logout/<lang>')
def logout(lang='ar'):
    session.pop('current_admin', None)
    return redirect(url_for('login', lang=lang))

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)