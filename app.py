from flask import Flask, render_template, request, redirect, url_for, flash, session
import os
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

# قاعدة بيانات بسيطة في الذاكرة للمسؤولين
admins_db = [
    {'name': 'أحمد محمد', 'email': '<EMAIL>', 'password': '123456', 'online': True},
    {'name': 'فاطمة علي', 'email': '<EMAIL>', 'password': '123456', 'online': False}
]

# اللغات المدعومة
LANGUAGES = {
    'ar': 'العربية',
    'en': 'English',
    'fr': 'Français'
}

# قاموس الترجمة
TRANSLATIONS = {
    'ar': {
        'تسجيل الدخول': 'تسجيل الدخول',
        'البريد الإلكتروني': 'البريد الإلكتروني',
        'كلمة السر': 'كلمة السر',
        'دخول': 'دخول',
        'لوحة تحكم المسؤولين': 'لوحة تحكم المسؤولين',
        'مرحبًا': 'مرحبًا',
        'تسجيل الخروج': 'تسجيل الخروج',
        'حذف': 'حذف',
        'اسم المسؤول': 'اسم المسؤول',
        'إضافة مسؤول': 'إضافة مسؤول',
        'بيانات الدخول غير صحيحة': 'بيانات الدخول غير صحيحة',
        'البريد الإلكتروني موجود مسبقاً': 'البريد الإلكتروني موجود مسبقاً',
        'تم إضافة المسؤول بنجاح': 'تم إضافة المسؤول بنجاح',
        'تم حذف المسؤول بنجاح': 'تم حذف المسؤول بنجاح'
    },
    'en': {
        'تسجيل الدخول': 'Login',
        'البريد الإلكتروني': 'Email',
        'كلمة السر': 'Password',
        'دخول': 'Login',
        'لوحة تحكم المسؤولين': 'Admin Dashboard',
        'مرحبًا': 'Welcome',
        'تسجيل الخروج': 'Logout',
        'حذف': 'Delete',
        'اسم المسؤول': 'Admin Name',
        'إضافة مسؤول': 'Add Admin',
        'بيانات الدخول غير صحيحة': 'Invalid login credentials',
        'البريد الإلكتروني موجود مسبقاً': 'Email already exists',
        'تم إضافة المسؤول بنجاح': 'Admin added successfully',
        'تم حذف المسؤول بنجاح': 'Admin deleted successfully'
    },
    'fr': {
        'تسجيل الدخول': 'Connexion',
        'البريد الإلكتروني': 'Email',
        'كلمة السر': 'Mot de passe',
        'دخول': 'Se connecter',
        'لوحة تحكم المسؤولين': 'Tableau de bord Admin',
        'مرحبًا': 'Bienvenue',
        'تسجيل الخروج': 'Déconnexion',
        'حذف': 'Supprimer',
        'اسم المسؤول': 'Nom Admin',
        'إضافة مسؤول': 'Ajouter Admin',
        'بيانات الدخول غير صحيحة': 'Identifiants invalides',
        'البريد الإلكتروني موجود مسبقاً': 'Email déjà existant',
        'تم إضافة المسؤول بنجاح': 'Admin ajouté avec succès',
        'تم حذف المسؤول بنجاح': 'Admin supprimé avec succès'
    }
}

# دالة الترجمة المحسنة
def _(text):
    current_lang = get_locale()
    return TRANSLATIONS.get(current_lang, {}).get(text, text)

def get_locale():
    return session.get('language', 'ar')

# جعل الدوال متاحة في جميع القوالب
@app.context_processor
def inject_template_vars():
    return dict(get_locale=get_locale, _=_)

@app.route('/')
@app.route('/<lang>')
def login(lang='ar'):
    session['language'] = lang
    return render_template('login.html')

@app.route('/', methods=['POST'])
@app.route('/<lang>', methods=['POST'])
def login_post(lang='ar'):
    session['language'] = lang
    email = request.form['email']
    password = request.form['password']

    # التحقق من بيانات المسؤول
    for admin in admins_db:
        if admin['email'] == email and admin['password'] == password:
            session['current_admin'] = admin['name']
            return redirect(url_for('dashboard', lang=lang))

    flash(_('بيانات الدخول غير صحيحة'))
    return render_template('login.html')

@app.route('/dashboard')
@app.route('/dashboard/<lang>')
def dashboard(lang='ar'):
    session['language'] = lang
    if 'current_admin' not in session:
        return redirect(url_for('login', lang=lang))

    return render_template('dashboard.html',
                         admins=admins_db,
                         current_admin=session['current_admin'])

@app.route('/add_admin/<lang>', methods=['POST'])
def add_admin(lang='ar'):
    if 'current_admin' not in session:
        return redirect(url_for('login', lang=lang))

    name = request.form['name']
    email = request.form['email']
    password = request.form['password']

    # التحقق من عدم وجود البريد الإلكتروني مسبقاً
    for admin in admins_db:
        if admin['email'] == email:
            flash(_('البريد الإلكتروني موجود مسبقاً'))
            return redirect(url_for('dashboard', lang=lang))

    # إضافة المسؤول الجديد
    admins_db.append({
        'name': name,
        'email': email,
        'password': password,
        'online': False
    })

    flash(_('تم إضافة المسؤول بنجاح'))
    return redirect(url_for('dashboard', lang=lang))

@app.route('/delete_admin/<email>/<lang>', methods=['POST'])
def delete_admin(email, lang='ar'):
    if 'current_admin' not in session:
        return redirect(url_for('login', lang=lang))

    global admins_db
    admins_db = [admin for admin in admins_db if admin['email'] != email]

    flash(_('تم حذف المسؤول بنجاح'))
    return redirect(url_for('dashboard', lang=lang))

@app.route('/logout/<lang>')
def logout(lang='ar'):
    session.pop('current_admin', None)
    return redirect(url_for('login', lang=lang))

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)